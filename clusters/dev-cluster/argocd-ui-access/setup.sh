#!/bin/bash

echo "== Setting up ArgoCD UI Access... =="

# Function to check if a resource exists and is ready
wait_for_resource() {
    local resource_type=$1
    local resource_name=$2
    local namespace=$3
    local condition=$4
    local timeout=${5:-300}
    
    echo "== Waiting for $resource_type/$resource_name to be $condition... =="
    if [ -n "$namespace" ]; then
        kubectl wait --for=condition=$condition $resource_type/$resource_name -n $namespace --timeout=${timeout}s
    else
        kubectl wait --for=condition=$condition $resource_type/$resource_name --timeout=${timeout}s
    fi
}

# Step 1: Apply ArgoCD server configuration
echo "== Applying ArgoCD server configuration... =="
kubectl apply -f ./resources/argocd-server-config.yaml

# Step 2: Request the SSL certificate
echo "== Requesting SSL certificate... =="
kubectl apply -f ./resources/argocd-certificate.yaml

# Wait for certificate to be ready
wait_for_resource "certificate" "argocd-tls-cert" "default" "Ready" 600

# Step 3: Create ReferenceGrant to allow Gateway to reference the TLS secret
echo "== Creating ReferenceGrant... =="
kubectl apply -f ./resources/argocd-reference-grant.yaml

# Step 4: Create Gateway for ArgoCD
echo "== Creating ArgoCD Gateway... =="
kubectl apply -f ./resources/argocd-gateway.yaml

# Wait for Gateway to be ready
wait_for_resource "gateway" "argocd-gateway" "default" "Programmed" 300

# Step 5: Create HTTPRoute for HTTPS traffic
echo "== Creating HTTPS HTTPRoute... =="
kubectl apply -f ./resources/argocd-https.yaml

# Step 6: Create HTTPRoute for HTTP to HTTPS redirect
echo "== Creating HTTP to HTTPS redirect... =="
kubectl apply -f ./resources/argocd-http2https-redirect.yaml

# Step 7: Restart ArgoCD server to pick up new configuration
echo "== Restarting ArgoCD server to apply configuration... =="
kubectl rollout restart deployment/argocd-server -n argocd
kubectl rollout status deployment/argocd-server -n argocd --timeout=300s

echo "== ArgoCD UI Access setup complete! =="
echo "== ArgoCD should be accessible at: https://argocd.wittig.nl =="
echo "== Note: DNS must be configured to point argocd.wittig.nl to your cluster's external IP =="
