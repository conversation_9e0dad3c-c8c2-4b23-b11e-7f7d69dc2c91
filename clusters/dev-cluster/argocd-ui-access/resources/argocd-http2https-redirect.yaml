apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: argocd-http2https-redirect
  namespace: default
spec:
  parentRefs:
    - kind: Gateway
      name: argocd-gateway
      namespace: default
      sectionName: http
  hostnames:
    - argocd.wittig.nl
  rules:
    - filters:
        - type: RequestRedirect
          requestRedirect:
            scheme: https
            statusCode: 301
