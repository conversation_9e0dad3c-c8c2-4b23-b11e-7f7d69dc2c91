apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: argocd-https
  namespace: default
spec:
  parentRefs:
    - kind: Gateway
      name: argocd-gateway
      namespace: default
      sectionName: https
  hostnames:
    - argocd.wittig.nl
  rules:
    - matches:
        - path:
            type: PathPrefix
            value: /
      backendRefs:
        - kind: Service
          name: argocd-server
          namespace: argocd
          port: 80
