apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: argocd-httproute
  namespace: default
  annotations:
    argocd.argoproj.io/sync-wave: "3"
spec:
  parentRefs:
    - kind: Gateway
      name: argocd-gateway
      namespace: default
  hostnames:
    - argocd.wittig.nl
  rules:
    # Redirect HTTP to HTTPS
    - matches:
        - path:
            type: PathPrefix
            value: /
      filters:
        - type: RequestRedirect
          requestRedirect:
            scheme: https
            statusCode: 301
      backendRefs: []
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: argocd-httproute-https
  namespace: default
  annotations:
    argocd.argoproj.io/sync-wave: "3"
spec:
  parentRefs:
    - kind: Gateway
      name: argocd-gateway
      namespace: default
      sectionName: https
  hostnames:
    - argocd.wittig.nl
  rules:
    - matches:
        - path:
            type: PathPrefix
            value: /
      backendRefs:
        - kind: Service
          name: argocd-server
          namespace: argocd
          port: 80
